/**
 * Centralized Logger Module
 * 
 * Provides a conditional logger that can be enabled or disabled for debugging.
 */

// This is a factory function because the logger needs to be instantiated
// differently in different script contexts (some don't have access to chrome.* APIs).
export function createLogger(debugMode) {
    const prefix = 'LLMLog:';
    return {
        log: (...args) => {
            if (debugMode) {
                console.log(prefix, ...args);
            }
        },
        error: (...args) => {
            // Errors should probably always be logged, but we'll respect the flag for now.
            if (debugMode) {
                console.error(prefix, ...args);
            }
        },
        warn: (...args) => {
            if (debugMode) {
                console.warn(prefix, ...args);
            }
        }
    };
}
