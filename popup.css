:root {
  --primary-bg: #f4f6f8;
  --secondary-bg: #ffffff;
  --text-color: #333;
  --border-color: #e0e0e0;
  --header-bg: #fff;
  --search-bg: #f0f0f0;
  --accent-color: #4a90e2;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  width: 450px;
  max-height: 580px;
  margin: 0;
  background-color: var(--primary-bg);
  color: var(--text-color);
  display: flex;
  flex-direction: column;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100%;
}

#detail-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.hidden {
  display: none !important;
}

.header {
  padding: 12px 16px;
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.detail-header h1 {
  margin: 0;
  font-size: 16px;
  text-align: left;
  flex-grow: 1;
}

.back-button {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 16px;
  padding: 5px 10px;
  border-radius: 4px;
}

.back-button:hover {
  background-color: #eef2f7;
}

h1 {
  font-size: 18px;
  margin: 0 0 10px 0;
  text-align: center;
}

.search-bar input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--search-bg);
  box-sizing: border-box;
  font-size: 14px;
}

#conversation-list, #conversation-detail {
  flex-grow: 1;
  overflow-y: auto;
  padding: 8px;
}

#conversation-detail {
  padding: 16px;
  background-color: var(--secondary-bg);
  border-radius: 8px;
  margin: 8px;
  border: 1px solid var(--border-color);
}

.empty-message {
  text-align: center;
  color: #888;
  padding: 40px;
}

.conversation-item {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.conversation-item:hover {
  background-color: #eef2f7;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.platform-badge {
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  color: #fff;
  text-transform: uppercase;
}

.platform-badge.chatgpt { background-color: #74ac9c; }
.platform-badge.gemini { background-color: #8e78b9; }
.platform-badge.claude { background-color: #d97757; }

.item-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.item-date {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
}

.item-preview {
  font-size: 13px;
  color: #555;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  color: #888;
  padding: 4px 8px;
  border-radius: 4px;
}

.menu-button:hover {
  background-color: #ddd;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h2 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 8px;
  color: #555;
}

.detail-content {
  word-break: break-word;
  line-height: 1.5;
}

.detail-meta {
  font-size: 12px;
  color: #888;
  margin-bottom: 15px;
}
/* --- Markdown Content Styling (Light Theme) --- */
.detail-content {
  line-height: 1.5;
  color: #24292e; /* Dark text for light background */
}

.detail-content h1,
.detail-content h2,
.detail-content h3,
.detail-content h4,
.detail-content h5,
.detail-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  border-bottom: 1px solid #eaecef; /* Lighter border */
  padding-bottom: 0.3em;
}

.detail-content h1 { font-size: 2em; }
.detail-content h2 { font-size: 1.5em; }
.detail-content h3 { font-size: 1.25em; }

.detail-content p {
  margin-bottom: 16px;
}

.detail-content a {
  color: #0366d6; /* Standard link color */
  text-decoration: none;
}

.detail-content a:hover {
  text-decoration: underline;
}

.detail-content ul,
.detail-content ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.detail-content li {
  margin-bottom: 0.25em;
}

.detail-content blockquote {
  margin: 0 0 16px 0;
  padding: 0 1em;
  color: #6a737d; /* Adjusted for light theme */
  border-left: 0.25em solid #dfe2e5; /* Lighter border */
}

.detail-content code {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
  margin: 0;
  background-color: rgba(27,31,35,0.05); /* Light background for code */
  border-radius: 6px;
}

.detail-content pre {
  position: relative; /* Required for positioning the copy button */
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa; /* Light background for code blocks */
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 16px;
}

.detail-content pre code {
  padding: 0;
  margin: 0;
  background-color: transparent;
  border-radius: 0;
  color: inherit; /* Inherit color from pre */
}

/* --- Copy Button Styling --- */
.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: inherit;
  cursor: pointer;
  opacity: 0; /* Hidden by default */
  transition: opacity 0.2s ease-in-out, background-color 0.2s;
}

.detail-content pre:hover .copy-button {
  opacity: 1; /* Show on hover */
}

.copy-button:hover {
  background-color: #d0d0d0;
}

.copy-button:active {
    background-color: #c0c0c0;
}