{"manifest_version": 3, "name": "LLMLog - Your Personal AI Knowledge Base", "version": "0.1.0", "description": "Automatically capture, manage, and search your conversations with various AI platforms.", "permissions": ["storage", "scripting", "activeTab", "tabs", "alarms"], "host_permissions": ["https://chat.openai.com/*", "https://gemini.google.com/*", "https://claude.ai/*"], "background": {"service_worker": "service-worker.js", "type": "module"}, "action": {"default_popup": "popup.html", "default_icon": "icons/icon.svg"}, "options_page": "debug.html", "icons": {"128": "icons/icon.svg"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://chat.openai.com https://gemini.google.com https://claude.ai https://chatgpt.com;"}, "web_accessible_resources": [{"resources": ["scripts/capture/interceptor.js", "scripts/capture/platforms/chatgpt.js", "scripts/capture/platforms/gemini.js", "scripts/capture/platforms/claude.js", "modules/logger.js"], "matches": ["https://chat.openai.com/*", "https://chatgpt.com/*", "https://gemini.google.com/*", "https://claude.ai/*"]}], "content_scripts": [{"matches": ["https://chat.openai.com/*", "https://chatgpt.com/*", "https://gemini.google.com/*", "https://claude.ai/*"], "js": ["scripts/capture/injector.js", "scripts/capture/bridge.js"], "run_at": "document_start"}]}