# 重構提案：LLMLog 內部架構現代化

**日期:** 2025年8月16日
**作者:** Kilo Code
**基於:** `TECHNICAL_RESEARCH_REPORT.md`

## 1. 核心問題分析

當前專案的內部架構存在幾個關鍵問題，限制了其長期發展：

*   **職責混雜:** `service-worker.js` 混合了資料庫管理、訊息監聽等多種職責，違反了單一職責原則。
*   **通訊耦合:** 擴充功能各部分之間的通訊依賴於一組鬆散的 `message.type` 字串，難以維護和擴展���
*   **數據模型隱式:** 對話數據的結構沒有明確定義，散佈在各個腳本的處理邏輯中。
*   **捕獲邏輯分散:** 新的 API 捕獲架構需要一個清晰的、可擴展的實現模式。

## 2. 重構目標

*   **模組化:** 將不同職責的程式碼分離到獨立的模組中。
*   **標準化:** 為內部通訊和數據模型定義清晰的規範。
*   **可擴展性:** 使添加對新 AI 平台（如 Gemini, Claude）的支援變得簡單、低風險。
*   **可測試性:** 使核心邏輯（如數據解析、資料庫操作）可以進行單元測試。

## 3. 重構方案

### 3.1. 修訂後的客戶端架構圖

```mermaid
graph TD
    subgraph "Content Scope (Per-Tab)"
        direction LR
        subgraph "Main World"
            A[api-interceptor.js] -- 1. postMessage --> B(content-bridge.js)
        end
        subgraph "Isolated World"
            C[content-injector.js] -- Injects --> A
            B -- 2. chrome.runtime.sendMessage --> D{Message Router}
        end
    end

    subgraph "Extension Scope (Singleton)"
        direction TB
        D -- 3. Dispatches --> E[Storage Module]
        D -- 4. Dispatches --> F[Settings Module]
        D -- 5. Dispatches --> G[Capture Module]
        
        subgraph "UI (Popup/Options)"
            H[popup.js] -- chrome.runtime.sendMessage --> D
            I[options.js] -- chrome.runtime.sendMessage --> D
        end
    end

    subgraph "Core Modules"
        E --> J[IndexedDB]
        F --> K[chrome.storage]
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#ccf,stroke:#333,stroke-width:2px
    style E fill:#cfc,stroke:#333,stroke-width:2px
    style F fill:#cfc,stroke:#333,stroke-width:2px
    style G fill:#cfc,stroke:#333,stroke-width:2px
```

### 3.2. 關鍵模組職責詳解

*   **捕獲層 (Capture Layer):**
    *   `content-injector.js`: 負責將攔截器注入到頁面主環境。
    *   `api-interceptor.js`: **(重構)** 應被設計成可配置的，接收特定於平台的配置（如 API 端點、解析規則）。
    *   `content-bridge.js`: **(新角色)** 作為橋樑，轉發消息並向 `api-interceptor` 提供平台配置。

*   **通訊層 (Communication Layer):**
    *   **`message-router.js` (新模組):** 作為一個中央訊息分發中樞，在 `service-worker.js` 中實現。它根據結構化的訊息將請求路由到正確的業務邏輯模組。

*   **業務邏輯層 (Business Logic Layer):**
    *   **`storage.js` (新模組):** 封裝所有與 IndexedDB 相關的操作，實現倉儲模式。
    *   **`settings.js` (新模組):** 封裝所有與 `chrome.storage` 相關的設定讀寫操作。
    *   **`capture.js` (新模組):** 包含與捕獲流程相關的邏輯，例如管理不同平台的配置。

### 3.3. 標準化的內部 API (訊息格式)

我們將定義一個結構化的訊息格式來取代鬆散的字串 `type`：

```javascript
// 範例：從 content-bridge 發送到 service-worker
chrome.runtime.sendMessage({
  namespace: 'storage', // 目標模組
  action: 'addConversation', // 執行的動作
  payload: { /* ... conversation data ... */ }
});

// 範例：從 popup 請求數據
chrome.runtime.sendMessage({
  namespace: 'storage',
  action: 'getAllConversations'
}, (response) => {
  // ... handle response
});
```

### 3.4. 設計決策說明

*   **訊息路由器模式:** 這是重構的核心，它將 `service-worker.js` 轉變為一個輕量級的事件分發器，極大地**提升了可維護性**。
*   **倉儲模式:** `storage.js` 模組將數據持久化邏輯與應用程式的其餘部分隔離開來，**提升了可測試性**和**靈活性**。
*   **依賴注入 (輕量級):** `content-bridge.js` 向 `api-interceptor.js` 提供平台配置，使得攔截器成為一個通用的、可重用的模組，從而**提升了可擴展性**。

## 4. 總結

這次重構將使 LLMLog 的內部架構從一個緊密耦合的腳本集合，演變為一個分層清晰、職責明確、易於擴展和維護的現代化客戶端應用。它直接解決了現有程式碼的擴展性問題，並為未來更複雜的功能奠定了堅實的基礎。