<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLMLog - Debug Console</title>
    <style>
        body { font-family: monospace; background-color: #1e1e1e; color: #d4d4d4; margin: 0; padding: 1rem; }
        h1 { color: #569cd6; }
        #controls { margin-bottom: 1rem; }
        button { background-color: #333; color: #d4d4d4; border: 1px solid #555; padding: 0.5rem 1rem; cursor: pointer; }
        button:hover { background-color: #444; }
        #log-container { white-space: pre-wrap; font-size: 0.9em; }
        .log-entry { padding: 0.25rem; border-bottom: 1px solid #333; }
        .log-info { color: #d4d4d4; }
        .log-warn { color: #cca700; }
        .log-error { color: #f44747; }
    </style>
</head>
<body>
    <h1>LLMLog - Debug Console</h1>
    <div id="controls">
        <button id="refresh-btn">Refresh</button>
        <button id="clear-btn">Clear Logs</button>
        <div class="setting" style="margin-top: 1rem;">
            <label for="debug-logging" style="cursor: pointer;">
                <input type="checkbox" id="debug-logging">
                Enable Debug Logging
            </label>
        </div>
    </div>
    <div id="log-container"></div>

    <script src="debug.js" type="module"></script>
</body>
</html>
