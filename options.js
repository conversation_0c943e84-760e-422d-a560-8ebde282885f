// This script will handle the logic for the options page.
// For example, loading and saving settings.
// And displaying statistics.

document.addEventListener('DOMContentLoaded', () => {
  const recordingEnabled = document.getElementById('recording-enabled');

  // Load saved setting
  chrome.storage.sync.get({ recordingEnabled: true }, (items) => {
    recordingEnabled.checked = items.recordingEnabled;
  });

  // Save setting on change
  recordingEnabled.addEventListener('change', () => {
    chrome.storage.sync.set({ recordingEnabled: recordingEnabled.checked });
  });

  document.getElementById('export-json').addEventListener('click', () => exportData('json'));
  document.getElementById('export-markdown').addEventListener('click', () => exportData('markdown'));

  loadStatistics();
});

function exportData(format) {
  chrome.runtime.sendMessage({ type: 'getAllConversations' }, (response) => {
    if (response.status === 'success') {
      const data = response.data;
      let content = '';
      let mimeType = '';
      let fileExtension = '';

      if (format === 'json') {
        content = JSON.stringify(data, null, 2);
        mimeType = 'application/json';
        fileExtension = 'json';
      } else if (format === 'markdown') {
        content = convertToMarkdown(data);
        mimeType = 'text/markdown';
        fileExtension = 'md';
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `llmlog_export_${new Date().toISOString().split('T')[0]}.${fileExtension}`;
      a.click();
      URL.revokeObjectURL(url);

    } else {
      console.error("Error exporting data:", response.message);
    }
  });
}

function convertToMarkdown(data) {
  let markdown = '# LLMLog Export\n\n';
  data.forEach(conv => {
    markdown += `## ${conv.title} (${conv.platform})\n\n`;
    markdown += `**URL:** ${conv.url}\n\n`;
    markdown += `**Date:** ${new Date(conv.createdAt).toLocaleString()}\n\n`;
    markdown += `### Prompt\n\n${conv.prompt}\n\n`;
    markdown += `### Response\n\n${conv.response}\n\n`;
    markdown += '---\n\n';
  });
  return markdown;
}

function loadStatistics() {
  chrome.runtime.sendMessage({ type: 'getAllConversations' }, (response) => {
    if (response.status === 'success') {
      const data = response.data;
      displayStatistics(data);
    } else {
      console.error("Error loading data for statistics:", response.message);
      document.getElementById('statistics-container').innerHTML = '<p>Error loading statistics.</p>';
    }
  });
}

function displayStatistics(data) {
  const container = document.getElementById('statistics-container');
  
  if (!data || data.length === 0) {
    container.innerHTML = '<p>No conversations recorded yet.</p>';
    return;
  }

  // Calculate statistics
  const totalConversations = data.length;
  const totalPrompts = data.length; // Each conversation has one prompt
  let totalPromptWords = 0;
  let totalResponseWords = 0;
  const platformCount = {};

  data.forEach(conv => {
    // Count words in prompt and response
    totalPromptWords += conv.prompt.split(/\s+/).filter(word => word.length > 0).length;
    totalResponseWords += conv.response.split(/\s+/).filter(word => word.length > 0).length;
    
    // Count platforms
    if (!platformCount[conv.platform]) {
      platformCount[conv.platform] = 0;
    }
    platformCount[conv.platform]++;
  });

  const avgPromptLength = totalPrompts > 0 ? (totalPromptWords / totalPrompts).toFixed(2) : 0;
  const avgResponseLength = totalConversations > 0 ? (totalResponseWords / totalConversations).toFixed(2) : 0;

  // Get most used platform
  let mostUsedPlatform = '';
  let maxCount = 0;
  for (const [platform, count] of Object.entries(platformCount)) {
    if (count > maxCount) {
      maxCount = count;
      mostUsedPlatform = platform;
    }
  }

  // Generate HTML for statistics
  let statsHTML = `
    <ul>
      <li><strong>Total Conversations:</strong> ${totalConversations}</li>
      <li><strong>Total Prompts:</strong> ${totalPrompts}</li>
      <li><strong>Total Prompt Words:</strong> ${totalPromptWords}</li>
      <li><strong>Total Response Words:</strong> ${totalResponseWords}</li>
      <li><strong>Average Prompt Length (words):</strong> ${avgPromptLength}</li>
      <li><strong>Average Response Length (words):</strong> ${avgResponseLength}</li>
      <li><strong>Most Used Platform:</strong> ${mostUsedPlatform} (${maxCount} conversations)</li>
    </ul>
  `;

  // Add platform breakdown
  statsHTML += `<h3>Platform Breakdown</h3><ul>`;
  for (const [platform, count] of Object.entries(platformCount)) {
    statsHTML += `<li>${platform}: ${count} conversations</li>`;
  }
  statsHTML += `</ul>`;

  container.innerHTML = statsHTML;
}