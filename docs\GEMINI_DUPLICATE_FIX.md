# Gemini 重複對話修復方案

## 🔍 **問題分析**

Gemini 平台會保存兩次對話的原因：

### 1. **API 調用特性**
- Gemini 使用 `StreamGenerate` 端點
- 該端點可能會發送多個 XHR 請求
- 每個請求都包含相同的對話數據
- 導致攔截器多次觸發保存邏輯

### 2. **原有重複檢測不足**
- 存儲層的 5 秒重複檢測窗口對 Gemini 不夠
- 攔截器層面沒有重複檢測
- 響應解析可能返回不完整數據

## 🛠️ **修復方案**

### 1. **攔截器層面重複檢測**

在 `scripts/capture/interceptor.js` 中添加：

```javascript
// 追蹤最近的對話以防止重複
const recentConversations = new Map();
const DUPLICATE_WINDOW_MS = 10000; // 10 秒

function isDuplicateConversation(conversationData) {
    const key = `${conversationData.platform}:${conversationData.prompt}:${conversationData.response}`;
    const now = Date.now();
    
    // 清理舊條目
    for (const [existingKey, timestamp] of recentConversations.entries()) {
        if (now - timestamp > DUPLICATE_WINDOW_MS) {
            recentConversations.delete(existingKey);
        }
    }
    
    // 檢查是否已存在
    if (recentConversations.has(key)) {
        return true;
    }
    
    // 添加到追蹤映射
    recentConversations.set(key, now);
    return false;
}
```

### 2. **存儲層改進**

在 `modules/storage.js` 中：

```javascript
// 為 Gemini 平台擴展重複檢測窗口
const duplicateWindow = conversationData.platform === 'Gemini' ? 15000 : 5000;

if (isDuplicateContent && timeDifference < duplicateWindow) {
    console.log('Duplicate conversation detected, skipping save.', { 
        platform: conversationData.platform, 
        timeDifference, 
        window: duplicateWindow 
    });
    return { status: 'success', data: { id: lastRecord.id, duplicate: true } };
}
```

### 3. **響應解析改進**

在 `scripts/capture/platforms/gemini.js` 中：

```javascript
// 確保只有完整響應才返回數據
if (fullText && fullText.trim().length > 0) {
    logger.log("Successfully extracted text and ID.", { fullText, conversationId });
    const url = conversationId ? `${window.location.origin}/app/${conversationId}` : null;
    return { text: fullText, id: conversationId, url };
}
```

### 4. **增強調試信息**

添加更詳細的日誌記錄：
- 請求和響應的時間戳
- 數據長度和預覽
- 重複檢測結果

## 📋 **修改的文件**

1. **`scripts/capture/interceptor.js`**
   - 添加重複對話追蹤機制
   - 在發送前檢查重複

2. **`modules/storage.js`**
   - 為 Gemini 擴展重複檢測窗口（15秒）
   - 改進重複檢測日誌

3. **`scripts/capture/platforms/gemini.js`**
   - 改進響應驗證邏輯
   - 增強調試信息

## 🧪 **測試方法**

### 使用測試腳本
1. 在 Gemini 頁面打開瀏覽器控制台
2. 複製並運行 `test-gemini-duplicate-fix.js` 的內容
3. 發送一條消息給 Gemini
4. 觀察 30 秒內的結果

### 手動測試
1. 啟用調試日誌
2. 在 Gemini 發送消息
3. 檢查控制台是否有重複檢測日誌
4. 檢查擴展的對話列表

## ✅ **預期結果**

修復後應該看到：

```
✅ 單次對話捕獲 - 無重複
LLMLog Bridge: Received conversation from interceptor.
Duplicate conversation detected, skipping. (如果有重複嘗試)
```

而不是：
```
❌ 相同對話被保存兩次
```

## 🔧 **故障排除**

如果仍然看到重複：

1. **檢查調試信息**：
   ```javascript
   window.llmlogDiagnostic()
   ```

2. **檢查控制台日誌**：
   - 尋找 "Duplicate conversation detected" 消息
   - 檢查 XHR 請求數量

3. **驗證平台檢測**：
   - 確保在 gemini.google.com 域名
   - 確保 Gemini 平台模塊已加載

4. **重新加載擴展**：
   - 如果問題持續，嘗試重新加載擴展

## 📊 **性能影響**

- **記憶體使用**：最小（僅追蹤 10 秒內的對話）
- **處理延遲**：可忽略（簡單字符串比較）
- **存儲影響**：減少（避免重複保存）

這個修復方案在攔截器和存儲層都提供了重複檢測，確保 Gemini 平台不會再保存重複的對話。
