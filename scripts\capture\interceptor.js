let platformModule;
let logger;

// Track recent conversations to prevent duplicates
const recentConversations = new Map();
const DUPLICATE_WINDOW_MS = 10000; // 10 seconds

// Function to check if a conversation is a duplicate
function isDuplicateConversation(conversationData) {
    const key = `${conversationData.platform}:${conversationData.prompt}:${conversationData.response}`;
    const now = Date.now();

    // Clean up old entries
    for (const [existingKey, timestamp] of recentConversations.entries()) {
        if (now - timestamp > DUPLICATE_WINDOW_MS) {
            recentConversations.delete(existingKey);
        }
    }

    // Check if this conversation already exists
    if (recentConversations.has(key)) {
        logger.log('Duplicate conversation detected, skipping.', { key });
        return true;
    }

    // Add this conversation to the tracking map
    recentConversations.set(key, now);
    return false;
}

// 1. Listen for messages from the injector and the platform module
window.addEventListener('message', (event) => {
    if (event.source !== window) return;

    if (event.data.type === 'LLMLOG_INIT') {
        const { modulePath, loggerPath, debugMode } = event.data.payload;
        
        import(loggerPath)
            .then(({ createLogger }) => {
                logger = createLogger(debugMode);
                logger.log('Interceptor initialized.', { debugMode });

                import(modulePath)
                    .then(module => {
                        platformModule = module;
                        logger.log('Platform module loaded.', platformModule.config.name);
                        if (platformModule.config.name === 'Gemini') {
                            overrideXHR();
                        } else {
                            overrideFetch();
                        }
                    })
                    .catch(e => logger.error('Failed to load platform module.', e));
            })
            .catch(e => console.error('LLMLog: Failed to load logger module.', e));
    }

    if (event.data.type === 'LLMLOG_CONVERSATION_UPDATE') {
        logger.log('Received conversation update from platform module.', event.data.payload);
        window.postMessage({ type: 'LLMLOG_CONVERSATION', payload: event.data.payload }, '*');
    }
});

// 2. Announce that the interceptor is ready and request the config
window.postMessage({ type: 'LLMLOG_INTERCEPTOR_READY' }, '*');


function overrideFetch() {
    if (!platformModule) {
        logger.error('No platform module loaded.');
        return;
    }

    const { config, parseRequest, parseResponse } = platformModule;
    const originalFetch = window.fetch;

    window.fetch = async (...args) => {
        const url = args[0] instanceof Request ? args[0].url : args[0];
        const method = (args[0] instanceof Request ? args[0].method : (args[1] ? args[1].method : 'GET'))?.toUpperCase();
        const requestUrl = new URL(url, window.location.origin);

        const isMatch = config.apiEndpoint instanceof RegExp
            ? config.apiEndpoint.test(requestUrl.pathname)
            : requestUrl.pathname === config.apiEndpoint;

        // Log requests for debugging
        logger.log('Saw a request.', { 
            method,
            pathname: requestUrl.pathname,
            expected: config.apiEndpoint.toString(),
            match: isMatch
        });

        // Intercept the target API call (POST or GET)
        if (isMatch) {
            const request = new Request(...args);
            logger.log('Target API call detected.', { url: requestUrl.href });

            const userPrompt = await parseRequest(request, logger);
            logger.log('Parsed user prompt.', { prompt: userPrompt });

            const response = await originalFetch(request);
            const responseClone = response.clone();

            const { text: aiResponse, id: conversationId, url: platformUrl } = await parseResponse(responseClone, logger);
            logger.log('Parsed AI response.', { response: aiResponse, conversationId, platformUrl });

            // For Claude, the conversation data is sent via a custom event, so we skip this part.
            if (config.name === 'Claude') {
                return response;
            }

            const conversationUrl = platformUrl || (conversationId
                ? `${window.location.origin}/c/${conversationId}`
                : window.location.href);

            const conversationData = {
                platform: config.name,
                prompt: userPrompt,
                response: aiResponse,
                url: conversationUrl,
                createdAt: new Date().toISOString(),
                title: userPrompt.substring(0, 50)
            };

            // Check for duplicates before sending
            if (!isDuplicateConversation(conversationData)) {
                window.postMessage({ type: 'LLMLOG_CONVERSATION', payload: conversationData }, '*');
                logger.log('Sent conversation data to bridge.', conversationData);
            }

            return response; // Return the original response, not a clone
        }

        // For all other requests, pass them through without modification.
        return originalFetch(...args);
    };

    logger.log('`fetch` has been overridden.');
}

function overrideXHR() {
    if (!platformModule) {
        logger.error('No platform module loaded for XHR.');
        return;
    }

    const { config, parseRequest, parseResponse } = platformModule;
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._llmlog_method = method;
        this._llmlog_url = url;
        return originalOpen.apply(this, [method, url, ...args]);
    };

    XMLHttpRequest.prototype.send = function(body) {
        const requestUrl = new URL(this._llmlog_url, window.location.origin);
        const method = this._llmlog_method.toUpperCase();

        if (method === 'POST') {
             logger.log('(XHR) Saw a POST request.', { 
                pathname: requestUrl.pathname,
                expected: config.apiEndpoint,
                match: requestUrl.pathname === config.apiEndpoint
            });
        }

        if (method === 'POST' && requestUrl.pathname === config.apiEndpoint) {
            logger.log('(XHR) Target API call detected.', { url: requestUrl.href });
            
            this.addEventListener('load', async () => {
                if (this.readyState === 4 && this.status === 200) {
                    logger.log('(XHR) Response loaded.');
                    
                    // Mock a Request object for parseRequest
                    const mockRequest = {
                        clone: () => mockRequest,
                        formData: async () => {
                            // The body is URL-encoded string, convert it to FormData
                            const params = new URLSearchParams(body);
                            const formData = new FormData();
                            for (const [key, value] of params.entries()) {
                                formData.append(key, value);
                            }
                            return formData;
                        }
                    };

                    const userPrompt = await parseRequest(mockRequest, logger);
                    logger.log('(XHR) Parsed user prompt.', { prompt: userPrompt });

                    // Mock a Response object for parseResponse
                    const mockResponse = {
                        clone: () => mockResponse,
                        text: async () => this.responseText,
                    };

                    const { text: aiResponse, id: conversationId, url: platformUrl } = await parseResponse(mockResponse, logger);
                    logger.log('(XHR) Parsed AI response.', { response: aiResponse, conversationId, platformUrl });

                    const conversationUrl = platformUrl || (conversationId
                        ? `${window.location.origin}/c/${conversationId}`
                        : window.location.href);

                    const conversationData = {
                        platform: config.name,
                        prompt: userPrompt,
                        response: aiResponse,
                        url: conversationUrl,
                        createdAt: new Date().toISOString(),
                        title: userPrompt.substring(0, 50)
                    };

                    // Check for duplicates before sending
                    if (!isDuplicateConversation(conversationData)) {
                        window.postMessage({ type: 'LLMLOG_CONVERSATION', payload: conversationData }, '*');
                        logger.log('(XHR) Sent conversation data to bridge.', conversationData);
                    }
                }
            });
        }
        
        return originalSend.apply(this, arguments);
    };
    
    logger.log('`XMLHttpRequest` has been overridden.');
}
