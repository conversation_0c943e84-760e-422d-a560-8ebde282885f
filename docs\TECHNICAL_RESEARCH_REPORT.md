# 技術研究報告：實現與前端解耦的對話捕獲方案

**日期:** 2025年8月16日
**作者:** Kilo Code

## 1. 問題陳述

本專案的現有對話捕獲機制**嚴重依賴於監聽 DOM 變化**來提取對話內容。此方法存在致命缺陷：**穩定性極差**。任何目標平台（如 ChatGPT）的前端 UI 更新，甚至是微小的 CSS 類名變更，都可能導致捕獲功能完全失效。

為了打造一個長期可靠、易於維護的產品，我們必須設計一套與前端界面完全解耦的、基於 API 接口監聽的全新對話捕獲解決方案。

## 2. 技術探索與概念驗證 (PoC)

我們對幾種潛在的技術方案進行了深入研究和驗證，過程如下：

### 2.1. 方案一：內容腳本 Monkey-Patching (失敗)

*   **假設：** 透過內容腳本覆寫頁面的 `window.fetch` 函數，可以直接攔截到 API 請求。
*   **過程：** 我們編寫了 `api-monitor.js` 腳本來實現攔截。
*   **結果：** **失敗**。基於 Chrome 擴充功能的沙盒機制，內容腳本運行在一個隔離的 JavaScript 環境 ("Isolated World") 中，其對 `window` 物件的修改無法影響頁面本身運行的主環境 ("Main World")。

### 2.2. 方案二：`chrome.debugger` API (技術上可行，但已放棄)

*   **假設：** 使用更底層的 `debugger` API，在瀏覽器層級監聽網路事件。
*   **過程：** 我們修改了 `manifest.json` 添加了 `debugger` 權限，並在 `service-worker.js` 中實現了附加偵錯器和監聽網路事件的邏輯。
*   **結果：** **技術上可行**，但存在重大使用者體驗問題。該 API 需要一個較為敏感的權限，會在安裝時向使用者顯示嚇人的警告，可能嚴重影響使用者的信任度和安裝意願。因此，我們決定放棄此方案，尋找侵入性更小的替代方案。

### 2.3. 方案三：腳本注入 Main World (成功！)

*   **假設：** 如果我們能將攔截腳本直接注入到頁面的 "Main World"，就能成功覆寫頁面自身使用的 `fetch` 函數。
*   **過程：**
    1.  創建了一個注入器腳本 `injector.js` (作為內容腳本運行)。
    2.  `injector.js` 的唯一作用是創建一個 `<script>` 標籤，將我們的攔截腳本 `api-monitor.js` 引入頁面。
    3.  在 `manifest.json` 中將 `api-monitor.js` 註冊為 `web_accessible_resources`，允許頁面加載它。
*   **結果：** **圓滿成功**。我們在頁面主控台中成功攔截到了 ChatGPT 的核心對話 API 請求。

## 3. 核心 API 分析 (以 ChatGPT 為例)

透過成功的 PoC，我們精確地識別了對話的 API 端點：

*   **API 端點:** `POST https://chatgpt.com/backend-api/f/conversation`
*   **使用者提問:** 包含在 `POST` 請求的 JSON `body` 中的 `messages` 陣列。
*   **AI 回覆:** 透過該端點返回的 `text/event-stream` (Server-Sent Events) 串流。我們可以透過監聽此串流並拼接數據塊，直到收到 `[DONE]` 標誌，來獲取完整的 AI 回答。

## 4. 新架構設計

基於已驗證的技術方案，我們設計了一套全新的、高可靠性的對話捕獲架構。

*   **核心組件:**
    1.  **`content-injector.js`:** 內容腳本，負責將攔截器注入到頁面主環境。
    2.  **`api-interceptor.js`:** 運行於頁面主環境，負責 `monkey-patch` `fetch` 函數，解析請求和回覆，並透過 `window.postMessage` 將數據發出。
    3.  **`content-bridge.js`:** 內容腳本，作為橋樑，監聽 `window.postMessage` 事件，並將數據轉發���背景腳本。
    4.  **`service-worker.js`:** 背景腳本，接收數據並將其存入 IndexedDB。

*   **數據流圖:**
    ```
    [Page Main World] --(fetch)--> [api-interceptor.js] --(postMessage)--> [content-bridge.js] --(sendMessage)--> [service-worker.js] --> [IndexedDB]
    ```

該架構實現了與 UI 的完全解耦，具備高穩定性和可維護性。

## 5. 實施路線圖

建議分階段實施新的捕獲方案：

*   **階段一：實現核心捕獲流程 (預計 3-5 天)**
    1.  創建 `content-injector.js`, `api-interceptor.js`, `content-bridge.js` 三個新檔案。
    2.  實現 `api-interceptor.js` 中針對 ChatGPT 的 `fetch` 攔截、請求解析和 SSE 串流解析邏輯。
    3.  建立 `main world` -> `isolated world` -> `background` 的完整通訊鏈路。
    4.  將解析出的對話數據成功存入 IndexedDB。

*   **階段二：重構與抽象化 (預計 2-3 天)**
    1.  將特定於平台的配置（如 API 端點 URL、解析規則）抽象成獨立的模組。
    2.  重構現有的 `scripts/content-*.js` 和 `scripts/extractor.js`，移除所有基於 DOM 的捕獲邏輯，改為僅注入適用於該平台的攔截器。

*   **階段三：擴展平台支援 (預計每個平台 2-4 天)**
    1.  重複我們的研究過程，分析 Gemini 和 Claude 的 API 端點。
    2.  為 Gemini 和 Claude 創建新的平台配置模組。
    3.  在擴充功能中實現對這兩個平台的 API 捕獲。

## 6. 結論

本次技術研究成功地解決了專案最核心的穩定性問題。我們驗證了一套基於**腳本注入**的 API 監聽方案，並設計了一套健壯、解耦的新架構。遵循此報告提出的路線圖，LLMLog 將能夠轉變為一個高可靠性且與前端界面無關的對話捕獲解決方案，為產品的長期發展奠定堅實的技術基礎。