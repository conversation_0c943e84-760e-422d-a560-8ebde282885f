import { initialize as initializeRouter } from './modules/router.js';
import { createLogger } from './modules/logger.js';
import { getSetting } from './modules/settings.js';

let logger;

// Keep the service worker alive
chrome.alarms.onAlarm.addListener(alarm => {
    if (alarm.name === 'keep-alive') {
        // This listener itself is enough to keep the service worker alive.
        // You can add a log here for debugging if needed.
        // logger?.log('Keep-alive alarm triggered.');
    }
});

// Initialize all core modules
async function main() {
    const debugLoggingEnabled = await getSetting('debugLoggingEnabled');
    logger = createLogger(debugLoggingEnabled);

    // Setup the keep-alive alarm
    chrome.alarms.create('keep-alive', {
        delayInMinutes: 0.1, // Start after 6 seconds
        periodInMinutes: 0.33 // Trigger every 20 seconds
    });

    // Handle connections from content scripts
    chrome.runtime.onConnect.addListener(port => {
        logger.log(`Connection established from ${port.name}`);
        port.onDisconnect.addListener(() => {
            logger.log(`Port ${port.name} disconnected.`);
        });
    });

    try {
        await initializeRouter();
        logger.log("LLMLog Service Worker initialized successfully.");
    } catch (e) {
        logger.error("Failed to initialize Service Worker:", e);
    }
}

main();
