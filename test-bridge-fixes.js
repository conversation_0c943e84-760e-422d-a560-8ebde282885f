/**
 * Test Script for LLMLog Bridge Fixes
 * 
 * This script can be run in the browser console to test the bridge functionality
 * and diagnose any remaining issues.
 */

console.log('=== LLMLog Bridge Test Script ===');

// Test 1: Check if diagnostic function is available
console.log('\n1. Testing diagnostic function...');
if (typeof window.llmlogDiagnostic === 'function') {
    const diagnostic = window.llmlogDiagnostic();
    console.log('✅ Diagnostic function available');
    console.log('Diagnostic info:', diagnostic);
    
    // Check platform detection
    if (diagnostic.platformModulePath) {
        console.log('✅ Platform module path detected:', diagnostic.platformModulePath);
    } else {
        console.log('❌ Platform module path not detected');
    }
    
    // Check interceptor readiness
    if (diagnostic.interceptorReady) {
        console.log('✅ Interceptor is ready');
    } else {
        console.log('⚠️ Interceptor not ready yet');
    }
    
    // Check service worker connection
    if (diagnostic.serviceWorkerPortConnected) {
        console.log('✅ Service worker port connected');
    } else {
        console.log('❌ Service worker port not connected');
    }
    
} else {
    console.log('❌ Diagnostic function not available - bridge may not be loaded');
}

// Test 2: Check if platform is supported
console.log('\n2. Testing platform detection...');
const hostname = window.location.hostname;
const supportedPlatforms = ['chat.openai.com', 'chatgpt.com', 'gemini.google.com', 'claude.ai'];
const isSupported = supportedPlatforms.some(platform => hostname.includes(platform.replace('.com', '')));

if (isSupported) {
    console.log('✅ Current platform is supported:', hostname);
} else {
    console.log('❌ Current platform is not supported:', hostname);
    console.log('Supported platforms:', supportedPlatforms);
}

// Test 3: Check extension context
console.log('\n3. Testing extension context...');
try {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
        console.log('✅ Extension context is valid');
        console.log('Extension ID:', chrome.runtime.id);
    } else {
        console.log('❌ Extension context is invalid');
    }
} catch (error) {
    console.log('❌ Error accessing extension context:', error.message);
}

// Test 4: Test message sending
console.log('\n4. Testing message sending...');
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
    chrome.runtime.sendMessage({
        namespace: 'settings',
        action: 'get',
        payload: { key: 'debugLoggingEnabled' }
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.log('❌ Error sending test message:', chrome.runtime.lastError.message);
        } else {
            console.log('✅ Test message sent successfully');
            console.log('Response:', response);
        }
    });
} else {
    console.log('❌ Cannot test message sending - chrome.runtime.sendMessage not available');
}

// Test 5: Check for interceptor script
console.log('\n5. Checking for interceptor script...');
const interceptorScripts = document.querySelectorAll('script[src*="interceptor.js"]');
if (interceptorScripts.length > 0) {
    console.log('✅ Interceptor script found:', interceptorScripts.length, 'instances');
} else {
    console.log('❌ Interceptor script not found');
}

// Test 6: Listen for bridge messages
console.log('\n6. Setting up message listener for 5 seconds...');
let messageCount = 0;
const messageListener = (event) => {
    if (event.source === window && event.data.type && event.data.type.startsWith('LLMLOG_')) {
        messageCount++;
        console.log(`📨 LLMLog message ${messageCount}:`, event.data.type, event.data.payload);
    }
};

window.addEventListener('message', messageListener);

setTimeout(() => {
    window.removeEventListener('message', messageListener);
    if (messageCount > 0) {
        console.log(`✅ Received ${messageCount} LLMLog messages`);
    } else {
        console.log('⚠️ No LLMLog messages received in 5 seconds');
    }
    
    console.log('\n=== Test Complete ===');
    console.log('If you see errors, check the browser console for more details.');
    console.log('You can run window.llmlogDiagnostic() anytime to get current status.');
}, 5000);

console.log('⏳ Listening for messages for 5 seconds...');
