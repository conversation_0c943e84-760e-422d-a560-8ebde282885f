# LLMLog 專案程式碼審查報告

**審查日期:** 2025年8月16日
**審查人:** Kilo Code (資深軟體架構師)

## 1. 總體評價

LLMLog 是一個架構精巧的瀏覽器擴充功能，它採用了現代化的 Manifest V3 標準和 IndexedDB 進行本地儲存，展現了良好的工程實踐基礎。其核心功能——透過主世界注入（Main World Injection）和 API 攔截來擷取對話——雖然功能強大，但也引入了顯著的維護性和穩定性挑戰。

本報告將從**架構**、**性能**、**程式碼品質**和**安全**四個維度，深入分析當前實作的優缺點，並提出具體、可執行的改進建議。

---

## 3. 性能瓶頸

### 3.1. 缺乏 IndexedDB 搜尋索引導致全表掃描

*   **問題描述:**
    資料庫只為 `createdAt` 建立了索引，導致搜尋功能只能將所有對話載入記憶體，然後在前端進行暴力篩選。

*   **風險評估:** **高**
    隨著對話紀錄增長，這會導致記憶體溢出���UI 凍結和極其緩慢的搜尋回應，嚴重影響產品的核心價值。

*   **具體程式碼位置/範例:**
    *   索引建立: [`modules/storage.js:23`](modules/storage.js:23)
    *   前端搜尋邏輯: [`popup.js:15`](popup.js:15)

*   **建議的修改方案:**
    1.  **建立「詞袋」索引:** 升級資料庫版本，並為對話的文字內容（`prompt` 和 `response`）建立一個 `multiEntry` 索引。
    2.  **後端搜尋:** 實作一個 `searchConversations(query)` 函式，利用新的索引在 IndexedDB 中高效執行搜尋，只回傳匹配的結果。

### 3.2. 在 UI 執行緒一次性載入所有對話

*   **問題描述:**
    `popup.js` 和 `options.js` 都會呼叫 `getAllConversations` 將所有對話一次性載入到前端記憶體中，用於顯示、搜尋、統計和匯出。

*   **風險評估:** **高**
    這是最直接影響使用者體驗的瓶頸。會導致 Popup 視窗開啟緩慢、搜尋卡頓，並在資料量大時造成瀏覽器資源浪費。

*   **具體程式碼位置/範例:**
    *   Popup 載入: [`popup.js:29`](popup.js:29)
    *   統計資料載入: [`options.js:70`](options.js:70)

*   **建議的修改方案:**
    1.  **實現分頁載入:** 修改 `getAllConversations` 函式，使其支援分頁參數（`page`, `pageSize`），並使用 `IDBCursor` 高效地讀取特定範圍的資料。
    2.  **改造 UI:** 在 `popup.js` 中實現「無限滾動」或分頁載入機制，取代目前的「一次性載入」模式。
    3.  **後端計算:** 將統計功能移至 Service Worker 中，透過 `IDBCursor` 進行計算，避免將大量原始資料傳輸到前端。

---

## 4. 程式碼品質與可讀性

### 4.1. 使用「魔術字串」進行跨腳本通訊

*   **問題描述:**
    在 `injector.js`, `interceptor.js`, 和 `bridge.js` 之間，通訊事件的類型（如 `'LLMLOG_INIT'`）是硬編碼的字串。

*   **風險評估:** **低**
    這使得程式碼難以維護，容易因打字錯誤而導致通訊失敗，且不易除錯。

*   **具體程式碼位置/範例:**
    *   [`scripts/capture/injector.js:45`](scripts/capture/injector.js:45)
    *   [`scripts/capture/interceptor.js:37`](scripts/capture/interceptor.js:37)

*   **建議的修改方案:**
    建立一個共用的 `modules/constants.js` 檔案來統一定義所有事件類型，並在各個腳本中匯入使用，以提高程式碼的健壯性和可維護性。

---

## 5. 安全性漏洞

### 5.1. `postMessage` 使用不安全的 `targetOrigin`

*   **問題描述:**
    在跨腳本通訊中，`window.postMessage` 的 `targetOrigin` 參數被設為 `*`，允許頁面上的任何來源的腳本監聽訊息。

*   **風險評估:** **中**
    如果目標網站存在 XSS 漏洞，或使用者安裝了其他惡意擴充功能，它們可以監聽到 `LLMLOG_CONVERSATION` 事件，從而竊取可能包含敏感資訊的完整對話紀錄。

*   **具體程式碼位置/範例:**
    *   [`scripts/capture/interceptor.js:98`](scripts/capture/interceptor.js:98)

*   **建議的修改方案:**
    將 `targetOrigin` 參數從 `*` 嚴格限制為 `window.location.origin`，確保訊息只被傳遞到同源的腳本。

### 5.2. 潛在的 DOM 型 XSS

*   **問題描述:**
    在 `popup.js` 的詳情視圖中，AI 的回應（可能包含 Markdown）是透過 `marked.parse()` 轉換為 HTML 後，直接用 `.innerHTML` 插入到頁面中，未有過濾。

*   **風險評估:** **中**
    如果 AI 的回應中包含惡意構造的 HTML/JavaScript 程式碼（例如 `<img>` 的 `onerror` 屬性），它將在擴充功能的 popup 環境中被執行，構成 XSS 漏洞。

*   **具體程式碼位置/範例:**
    *   [`popup.js:109`](popup.js:109)

*   **建議的修改方案:**
    在將 `marked.parse()` 的輸出插入到 DOM 之前，必須使用一個可靠的 HTML 淨化函式庫（如 **DOMPurify**）對其進行過濾，移除所有潛在的危險標籤和屬性。

---

## 6. 優先級排序

根據問題的嚴重性和對使用者體驗的影響，建議按以下順序處理：

*   **高 (立即處理):**
    1.  **性能 - 在 UI 一次性載入所有對話:** 這是最影響核心體驗的瓶頸。
    2.  **性能 - 缺乏搜尋索引:** 解決此問題是實現高效能搜尋的基礎。

*   **中 (計畫處理):**
    1.  **安全 - 不安全的 `targetOrigin`:** 保護使用者資料，應優先修復。
    2.  **安全 - 潛在的 DOM 型 XSS:** 防止惡意腳本執行。

*   **低 (擇機處理):**
    1.  **品質 - 使用魔術字串:** 屬於程式碼健康度問題，可在開發新功能時一併重構。