# LLMLog 專案程式碼審查報告

**審查日期:** 2025年8月16日
**審查人:** Kilo Code (資深軟體架構師)

## 1. 總體評價

LLMLog 是一個架構精巧的瀏覽器擴充功能，它採用了現代化的 Manifest V3 標準和 IndexedDB 進行本地儲存，展現了良好的工程實踐基礎。其核心功能——透過主世界注入（Main World Injection）和 API 攔截來擷取對話——雖然功能強大，但也引入了顯著的維護性和穩定性挑戰。

本報告將從**架構**、**性能**、**程式碼品質**和**安全**四個維度，深入分析當前實作的優缺點，並提出具體、可執行的改進建議。

---

## 2. 架構與設計模式

### 2.1. 高度耦合的 API 攔截架構

*   **問題描述:**
    專案的核心擷取機制依賴於「猴子補丁」（Monkey-Patching）來覆寫 `window.fetch` 和 `XMLHttpRequest`，並攔截特定、硬編碼的 API 端點（如 ChatGPT 的 `/backend-api/f/conversation`）。這種設計與目標網站的前端實作高度耦合。

*   **風險評估:** **高**
    這是專案最主要的架構風險。一旦目標網站更新其前端，例如更改 API 路徑、修改請求/回應的 JSON 結構，擷取功能將立即失效。這會導致極高的維護成本和不穩定的使用者體驗。

*   **具體程式碼位置/範例:**
    *   API 端點硬編��: [`scripts/capture/platforms/chatgpt.js:7`](scripts/capture/platforms/chatgpt.js:7)
    *   `fetch` API 覆寫: [`scripts/capture/interceptor.js:49`](scripts/capture/interceptor.js:49)

*   **建議的修改方案:**
    1.  **短期 (加固):** 建立一個遠端設定檔伺服器。擴充功能在啟動時動態獲取最新的 API 端點和解析規則。當平台更新時，只需修改遠端設定檔即可快速恢復功能，無需重新發布擴充功能。
    2.  **長期 (備援):** 研究並實作一個基於 DOM 爬取的備用擷取方案。當 API 攔截失敗時，可自動切換到 DOM 爬取模式，以提高系統的韌性。

---

## 3. 性能瓶頸

### 3.1. 缺乏 IndexedDB 搜尋索引導致全表掃描

*   **問題描述:**
    資料庫只為 `createdAt` 建立了索引，導致搜尋功能只能將所有對話載入記憶體，然後在前端進行暴力篩選。

*   **風險評估:** **高**
    隨著對話紀錄增長，這會導致記憶體溢出���UI 凍結和極其緩慢的搜尋回應，嚴重影響產品的核心價值。

*   **具體程式碼位置/範例:**
    *   索引建立: [`modules/storage.js:23`](modules/storage.js:23)
    *   前端搜尋邏輯: [`popup.js:15`](popup.js:15)

*   **建議的修改方案:**
    1.  **建立「詞袋」索引:** 升級資料庫版本，並為對話的文字內容（`prompt` 和 `response`）建立一個 `multiEntry` 索引。
    2.  **後端搜尋:** 實作一個 `searchConversations(query)` 函式，利用新的索引在 IndexedDB 中高效執行搜尋，只回傳匹配的結果。

### 3.2. 在 UI 執行緒一次性載入所有對話

*   **問題描述:**
    `popup.js` 和 `options.js` 都會呼叫 `getAllConversations` 將所有對話一次性載入到前端記憶體中，用於顯示、搜尋、統計和匯出。

*   **風險評估:** **高**
    這是最直接影響使用者體驗的瓶頸。會導致 Popup 視窗開啟緩慢、搜尋卡頓，並在資料量大時造成瀏覽器資源浪費。

*   **具體程式碼位置/範例:**
    *   Popup 載入: [`popup.js:29`](popup.js:29)
    *   統計資料載入: [`options.js:70`](options.js:70)

*   **建議的修改方案:**
    1.  **實現分頁載入:** 修改 `getAllConversations` 函式，使其支援分頁參數（`page`, `pageSize`），並使用 `IDBCursor` 高效地讀取特定範圍的資料。
    2.  **改造 UI:** 在 `popup.js` 中實現「無限滾動」或分頁載入機制，取代目前的「一次性載入」模式。
    3.  **後端計算:** 將統計功能移至 Service Worker 中，透過 `IDBCursor` 進行計算，避免將大量原始資料傳輸到前端。

---

## 4. 程式碼品質與可讀性

### 4.1. 使用「魔術字串」進行跨腳本通訊

*   **問題描述:**
    在 `injector.js`, `interceptor.js`, 和 `bridge.js` 之間，通訊事件的類型（如 `'LLMLOG_INIT'`）是硬編碼的字串。

*   **風險評估:** **低**
    這使得程式碼難以維護，容易因打字錯誤而導致通訊失敗，且不易除錯。

*   **具體程式碼位置/範例:**
    *   [`scripts/capture/injector.js:45`](scripts/capture/injector.js:45)
    *   [`scripts/capture/interceptor.js:37`](scripts/capture/interceptor.js:37)

*   **建議的修改方案:**
    建立一個共用的 `modules/constants.js` 檔案來統一定義所有事件類型，並在各個腳本中匯入使用，以提高程式碼的健壯性和可維護性。

### 4.2. 為重用程式碼而建立脆弱的「模擬物件」

*   **問題描述:**
    在 `interceptor.js` 中，為了讓 `XMLHttpRequest` 的攔截邏輯能重用 `fetch` 的解析函式，程式碼手動建立了模擬的 `Request` 和 `Response` 物件。

*   **風險評估:** **中**
    這種作法建立了隱性的耦合。如果未來解析函式需要用到模擬物件上不存在的新屬性或方法，`XHR` 的攔截邏輯將會 silently fail。

*   **具體程式碼位置/範例:**
    *   [`scripts/capture/interceptor.js:147-167`](scripts/capture/interceptor.js:147)

*   **建議的修改方案:**
    重構 `parseRequest` 和 `parseResponse` 函式，使其接受更原始的資料類型（如 JSON 物件或字串），而不是依賴整個 `Request` / `Response` API。

---

## 5. 安全性漏洞

### 5.1. `postMessage` 使用不安全的 `targetOrigin`

*   **問題描述:**
    在跨腳本通訊中，`window.postMessage` 的 `targetOrigin` 參數被設為 `*`，允許頁面上的任何來源的腳本監聽訊息。

*   **風險評估:** **中**
    如果目標網站存在 XSS 漏洞，或使用者安裝了其他惡意擴充功能，它們可以監聽到 `LLMLOG_CONVERSATION` 事件，從而竊取可能包含敏感資訊的完整對話紀錄。

*   **具體程式碼位置/範例:**
    *   [`scripts/capture/interceptor.js:98`](scripts/capture/interceptor.js:98)

*   **建議的修改方案:**
    將 `targetOrigin` 參數從 `*` 嚴格限制為 `window.location.origin`，確保訊息只被傳遞到同源的腳本。

### 5.2. 潛在的 DOM 型 XSS

*   **問題描述:**
    在 `popup.js` 的詳情視圖中，AI 的回應（可能包含 Markdown）是透過 `marked.parse()` 轉換為 HTML 後，直接用 `.innerHTML` 插入到頁面中，未��過濾。

*   **風險評估:** **中**
    如果 AI 的回應中包含惡意構造的 HTML/JavaScript 程式碼（例如 `<img>` 的 `onerror` 屬性），它將在擴充功能的 popup 環境中被執行，構成 XSS 漏洞。

*   **具體程式碼位置/範例:**
    *   [`popup.js:109`](popup.js:109)

*   **建議的修改方案:**
    在將 `marked.parse()` 的輸出插入到 DOM 之前，必須使用一個可靠的 HTML 淨化函式庫（如 **DOMPurify**）對其進行過濾，移除所有潛在的危險標籤和屬性。

---

## 6. 優先級排序

根據問題的嚴重性和對使用者體驗的影響，建議按以下順序處理：

*   **高 (立即處理):**
    1.  **性能 - 在 UI 一次性載入所有對話:** 這是最影響核心體驗的瓶頸。
    2.  **性能 - 缺乏搜尋索引:** 解決此問題是實現高效能搜尋的基礎。
    3.  **架構 - 高度耦合的 API 攔截:** 雖然是架構問題，但其「易碎性」對核心功能的穩定性構成直接威脅，應盡快實施加固方案。

*   **中 (計畫處理):**
    1.  **安全 - 不安全的 `targetOrigin`:** 保護使用者資料，應優先修復。
    2.  **安全 - 潛在的 DOM 型 XSS:** 防止惡意腳本執行。
    3.  **品質 - 脆弱的模擬物件:** 降低未來維護成本。

*   **低 (擇機處理):**
    1.  **品質 - 使用魔術字串:** 屬於程式碼健康度問題，可在開發新功能時一併重構。