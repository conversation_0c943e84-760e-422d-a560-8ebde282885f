/**
 * Platform Logic for Claude.ai
 */

export const config = {
    name: '<PERSON>',
    // This is a regex to match the chat conversation API endpoint
    apiEndpoint: /^\/api\/organizations\/[a-f0-9-]+\/chat_conversations\/[a-f0-9-]+$/,
};

// We don't need to parse the request for <PERSON>, as all data is in the response.
export async function parseRequest(request, logger) {
    return '';
}

export async function parseResponse(response, logger) {
    logger.log("Starting to parse response...");
    try {
        const data = await response.clone().json();
        const conversationId = data.uuid;
        
        if (data.chat_messages && data.chat_messages.length >= 2) {
            const lastTwoMessages = data.chat_messages.slice(-2);
            const userMessage = lastTwoMessages.find(m => m.sender === 'human');
            const assistantMessage = lastTwoMessages.find(m => m.sender === 'assistant');

            if (userMessage && assistantMessage) {
                const userPrompt = userMessage.content.map(c => c.text).join('\n');
                const aiResponse = assistantMessage.content.map(c => c.text).join('\n');

                logger.log("Successfully extracted prompt and response.", { userPrompt, aiResponse, conversationId });
                
                // We need to post a message back to the injector with the full conversation data
                // as we cannot get the prompt from the request.
                window.postMessage({
                    type: 'LLMLOG_CONVERSATION_UPDATE',
                    payload: {
                        platform: config.name,
                        prompt: userPrompt,
                        response: aiResponse,
                        url: window.location.href,
                        createdAt: new Date().toISOString(),
                        title: userPrompt.substring(0, 50)
                    }
                }, '*');

                return { text: aiResponse, id: conversationId };
            }
        }
    } catch (e) {
        logger.error("Error parsing Claude response:", e);
    }
    
    logger.log("Could not extract conversation data.");
    return { text: '', id: null };
}
